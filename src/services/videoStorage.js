/**
 * Video Storage Service
 * Stores video recordings to AWS S3
 */

import { uploadVideoToS3 } from './awsStorage';

// In-memory storage for recorded videos
const videoStore = {
  recordings: [],
  
  /**
   * Add a video recording to the in-memory store
   * @param {Blob} videoBlob - The video blob
   * @param {string} phrase - The phrase being spoken
   * @param {string} category - The category of the phrase
   * @param {number} recordingNumber - The recording number (1, 2, or 3)
   * @returns {string} - ID of the stored recording
   */
  addRecording: (videoBlob, phrase, category, recordingNumber) => {
    const id = `${category}-${phrase}-${recordingNumber}-${Date.now()}`;
    
    videoStore.recordings.push({
      id,
      videoBlob,
      phrase,
      category,
      recordingNumber,
      timestamp: new Date().toISOString()
    });
    
    console.log(`Video recording stored in memory: ${id}`);
    console.log(`Total recordings in memory: ${videoStore.recordings.length}`);
    
    return id;
  },

  /**
   * Save a single recording to AWS S3
   * @param {Blob} videoBlob - The video blob
   * @param {Object} metadata - Recording metadata
   * @returns {Promise<Object>} - Saved recording data
   */
  saveRecording: async (videoBlob, metadata) => {
    try {
      console.log('=== VIDEO STORAGE: Starting save process ===');
      console.log('🎬 Video blob details:', {
        size: videoBlob.size,
        type: videoBlob.type,
        isValidBlob: videoBlob instanceof Blob
      });
      console.log('📋 Metadata received:', metadata);
      console.log('🔧 Environment check:', {
        backendUrl: process.env.REACT_APP_BACKEND_URL,
        hasUploadFunction: typeof uploadVideoToS3 === 'function'
      });

      // Extract demographics from metadata
      const demographics = {
        userId: metadata.userId || 'user01',
        ageGroup: metadata.ageGroup || '40to64',
        gender: metadata.gender || 'female',
        ethnicity: metadata.ethnicity || 'not_specified'
      };
      console.log('Demographics extracted:', demographics);

      console.log('Calling uploadVideoToS3...');
      console.log('Recording number for upload:', metadata.recordingNumber);
      // Upload to S3
      const result = await uploadVideoToS3(videoBlob, metadata.phrase, demographics, metadata.recordingNumber);
      console.log('uploadVideoToS3 result:', result);

      const savedData = {
        id: metadata.id || `recording-${Date.now()}`,
        url: result.url,
        key: result.key,
        filename: result.filename,
        phrase: metadata.phrase,
        category: metadata.category,
        timestamp: new Date().toISOString()
      };

      console.log('=== VIDEO STORAGE: Save process completed successfully ===');
      console.log('Final saved data:', savedData);

      return savedData;
    } catch (error) {
      console.error('=== VIDEO STORAGE: Error during save process ===');
      console.error('Error details:', error);
      console.error('Error stack:', error.stack);
      throw new Error(`Failed to save recording: ${error.message}`);
    }
  },
  
  /**
   * Get all stored recordings
   * @returns {Array} - Array of stored recordings
   */
  getAllRecordings: () => {
    return [...videoStore.recordings];
  },
  
  /**
   * Clear all stored recordings
   */
  clearRecordings: () => {
    videoStore.recordings = [];
    console.log('All recordings cleared from memory');
  },
  
  /**
   * Save all stored recordings to AWS S3
   * @param {Object} demographics - Demographics information
   * @returns {Promise<Array>} - Array of saved recording paths
   */
  saveAllRecordings: async (demographics) => {
    try {
      console.log(`Saving ${videoStore.recordings.length} recordings to AWS S3...`);
      
      const savedPaths = [];
      
      for (const recording of videoStore.recordings) {
        try {
          const result = await uploadVideoToS3(recording.videoBlob, recording.phrase, demographics, recording.recordingNumber || 1);
          
          savedPaths.push({
            id: recording.id,
            url: result.url,
            key: result.key,
            filename: result.filename,
            phrase: recording.phrase,
            category: recording.category
          });
          
          console.log(`Saved recording ${recording.id} to S3`);
        } catch (error) {
          console.error(`Failed to save recording ${recording.id}:`, error);
          // Continue with other recordings even if one fails
        }
      }
      
      // Clear the recordings after processing
      videoStore.clearRecordings();
      
      return savedPaths;
    } catch (error) {
      console.error('Error saving recordings to S3:', error);
      throw error;
    }
  }
};

export default videoStore;
